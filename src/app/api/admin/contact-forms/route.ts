import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/contact-forms - Get all contact forms with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const url = new URL(request.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const limit = parseInt(url.searchParams.get('limit') || '10')
  const search = url.searchParams.get('search') || ''
  const sortBy = url.searchParams.get('sortBy') || 'createdat'
  const sortOrder = url.searchParams.get('sortOrder') || 'desc'
  const status = url.searchParams.get('status') || ''
  const isread = url.searchParams.get('isread') || ''

  const skip = (page - 1) * limit

  // Build where clause
  const where: any = {}

  // Add search functionality
  if (search && search.trim()) {
    const searchTerm = search.trim()
    where.OR = [
      { name: { contains: searchTerm, mode: 'insensitive' } },
      { email: { contains: searchTerm, mode: 'insensitive' } },
      { subject: { contains: searchTerm, mode: 'insensitive' } },
      { message: { contains: searchTerm, mode: 'insensitive' } },
    ]
  }

  // Add status filter
  if (status) {
    where.status = status
  }

  // Add read status filter
  if (isread !== '') {
    where.isread = isread === 'true'
  }

  // Build sort query
  const orderBy: any = {}
  orderBy[sortBy] = sortOrder

  // Get contact forms with pagination
  const [contactForms, total] = await Promise.all([
    prisma.contactforms.findMany({
      where,
      orderBy,
      skip,
      take: limit,
    }),
    prisma.contactforms.count({ where })
  ])

  // Transform BigInt to string for JSON serialization
  const transformedContactForms = contactForms.map(form => ({
    ...form,
    id: form.id.toString(),
    parentid: form.parentid?.toString() || null,
    threadid: form.threadid?.toString() || null,
    senderid: form.senderid?.toString() || null,
    receiverid: form.receiverid?.toString() || null,
    createdAt: form.createdat?.toISOString(),
    updatedAt: form.updatedat?.toISOString(),
  }))

  return Response.json({
    data: transformedContactForms,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit)
  })
})

// POST /api/admin/contact-forms - Create a new contact form
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()

  // Validate the data
  const validate = validateRequest(schemas.contactForm.create)
  const data = await validate(request)

  const contactForm = await prisma.contactforms.create({
    data: {
      name: data.name,
      email: data.email,
      phone: data.phone || null,
      subject: data.subject,
      message: data.message,
      isread: data.isread || false,
      readat: data.isread ? new Date() : null,
      status: data.status || 'New',
    }
  })

  // Send email notification if this is a new submission (not marked as read)
  if (!data.isread) {
    try {
      const { sendContactFormNotification } = await import('@/lib/email')
      await sendContactFormNotification({
        name: data.name,
        email: data.email,
        phone: data.phone,
        subject: data.subject,
        message: data.message,
      })
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError)
      // Don't fail the request if email fails, but log it
    }
  }

  // Transform BigInt to string for JSON serialization
  const transformedContactForm = {
    ...contactForm,
    id: contactForm.id.toString(),
    parentid: contactForm.parentid?.toString() || null,
    threadid: contactForm.threadid?.toString() || null,
    senderid: contactForm.senderid?.toString() || null,
    receiverid: contactForm.receiverid?.toString() || null,
    createdAt: contactForm.createdat?.toISOString(),
    updatedAt: contactForm.updatedat?.toISOString(),
  }

  return Response.json({
    success: true,
    data: transformedContactForm,
    message: 'Contact form created successfully'
  }, { status: 201 })
})
