'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useSession } from 'next-auth/react'

interface ChatMessage {
  id: string | number
  name: string
  email: string
  message: string
  messageType: string
  contentType: string
  createdAt: string
  isRead?: boolean
  attachments?: Array<{
    id: string
    filename: string
    size: number
    mimeType: string
    url: string
  }>
  sender?: {
    id: string | number
    email: string
    firstname?: string
    lastname?: string
    imageurl?: string
    role: string
  }
  receiver?: {
    id: string | number
    email: string
    firstname?: string
    lastname?: string
    imageurl?: string
    role: string
  }
  parent?: {
    id: string | number
    subject: string
    message: string
    createdAt: string
  }
}

interface UseChatRealtimeOptions {
  contactFormId: string | number
  enabled?: boolean
  pollingInterval?: number
  onNewMessage?: (message: ChatMessage) => void
  onMessageUpdate?: (message: ChatMessage) => void
}

export function useChatRealtime({
  contactFormId,
  enabled = true,
  pollingInterval = 30000, // 30 seconds - reduced from 3 seconds to minimize server load
  onNewMessage,
  onMessageUpdate
}: UseChatRealtimeOptions) {
  const { data: session } = useSession()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastFetchTime, setLastFetchTime] = useState<Date | null>(null)

  const intervalRef = useRef<NodeJS.Timeout>()
  const lastMessageCountRef = useRef(0)
  const isActiveRef = useRef(true)
  const lastActivityRef = useRef(Date.now())
  const idleTimeoutRef = useRef<NodeJS.Timeout>()

  // Determine API endpoint based on user role
  const getApiEndpoint = useCallback((endpoint: string) => {
    const isAdmin = session?.user?.role === 'ADMIN'
    const baseUrl = isAdmin ? '/api/admin' : '/api/chat'
    return `${baseUrl}/contact-forms/${contactFormId}/${endpoint}`
  }, [contactFormId, session?.user?.role])

  // Fetch messages from API
  const fetchMessages = useCallback(async (isInitial = false) => {
    if (!contactFormId || !enabled) return

    try {
      if (isInitial) {
        setIsLoading(true)
      }
      
      const response = await fetch(getApiEndpoint('messages'))
      
      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.status}`)
      }

      const data = await response.json()
      const newMessages = data.data?.messages || []
      
      setMessages(prevMessages => {
        // Check for new messages
        if (newMessages.length > lastMessageCountRef.current) {
          const newMessagesList = newMessages.slice(lastMessageCountRef.current)
          newMessagesList.forEach(message => {
            onNewMessage?.(message)
          })
        }
        
        // Check for updated messages
        if (!isInitial && prevMessages.length > 0) {
          newMessages.forEach(newMessage => {
            const existingMessage = prevMessages.find(m => m.id === newMessage.id)
            if (existingMessage && 
                (existingMessage.isRead !== newMessage.isRead || 
                 existingMessage.message !== newMessage.message)) {
              onMessageUpdate?.(newMessage)
            }
          })
        }
        
        lastMessageCountRef.current = newMessages.length
        return newMessages
      })
      
      setLastFetchTime(new Date())
      setError(null)
      
    } catch (err) {
      console.error('Failed to fetch messages:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch messages')
    } finally {
      if (isInitial) {
        setIsLoading(false)
      }
    }
  }, [contactFormId, enabled, onNewMessage, onMessageUpdate])

  // Start polling with idle detection
  const startPolling = useCallback(() => {
    if (!enabled || intervalRef.current) return

    intervalRef.current = setInterval(() => {
      if (isActiveRef.current) {
        // Check if user has been idle for more than 5 minutes
        const idleTime = Date.now() - lastActivityRef.current
        const maxIdleTime = 5 * 60 * 1000 // 5 minutes

        if (idleTime < maxIdleTime) {
          fetchMessages(false)
        } else {
          // Stop polling if user is idle for too long
          console.log('Chat polling paused due to inactivity')
          stopPolling()
        }
      }
    }, pollingInterval)
  }, [enabled, pollingInterval, fetchMessages])

  // Stop polling
  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = undefined
    }
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current)
      idleTimeoutRef.current = undefined
    }
  }, [])

  // Track user activity
  const trackActivity = useCallback(() => {
    lastActivityRef.current = Date.now()
    // Restart polling if it was stopped due to inactivity
    if (!intervalRef.current && enabled) {
      startPolling()
    }
  }, [enabled, startPolling])

  // Manual refresh
  const refresh = useCallback(() => {
    return fetchMessages(false)
  }, [fetchMessages])

  // Handle visibility change to pause/resume polling when tab is not active
  useEffect(() => {
    const handleVisibilityChange = () => {
      isActiveRef.current = !document.hidden
      
      if (document.hidden) {
        stopPolling()
      } else if (enabled) {
        startPolling()
        // Fetch immediately when tab becomes active
        fetchMessages(false)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [enabled, startPolling, stopPolling, fetchMessages])

  // Initialize and manage polling
  useEffect(() => {
    if (enabled && contactFormId) {
      // Initial fetch
      fetchMessages(true)
      // Start polling
      startPolling()
    } else {
      stopPolling()
    }

    return () => {
      stopPolling()
    }
  }, [enabled, contactFormId, fetchMessages, startPolling, stopPolling])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling()
      isActiveRef.current = false
    }
  }, [stopPolling])

  // Mark message as read
  const markAsRead = useCallback(async (messageId: string | number) => {
    try {
      const isAdmin = session?.user?.role === 'ADMIN'
      const statusEndpoint = isAdmin
        ? `/api/admin/contact-forms/messages/${messageId}/status`
        : `/api/chat/messages/${messageId}/status`

      const response = await fetch(statusEndpoint, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isread: true })
      })

      if (response.ok) {
        // Update local state immediately for better UX
        setMessages(prev => prev.map(msg => 
          msg.id === messageId ? { ...msg, isRead: true } : msg
        ))
        // Refresh to get server state
        await refresh()
      }
    } catch (error) {
      console.error('Failed to mark message as read:', error)
    }
  }, [refresh])

  // Send new message
  const sendMessage = useCallback(async (
    message: string,
    attachments: Array<{ id: string; filename: string; size: number; mimeType: string; url: string; path: string }> = []
  ) => {
    try {
      // Track activity when sending message
      trackActivity()

      const response = await fetch(getApiEndpoint('messages'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message,
          attachments,
          messagetype: 'chat',
          contenttype: 'text'
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send message')
      }

      // Refresh messages immediately
      await refresh()

      return true
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }, [contactFormId, refresh, trackActivity])

  return {
    messages,
    isLoading,
    error,
    lastFetchTime,
    refresh,
    markAsRead,
    sendMessage,
    startPolling,
    stopPolling,
    trackActivity
  }
}
