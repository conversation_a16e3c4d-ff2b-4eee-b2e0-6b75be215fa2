'use client'

import { useState, useCallback, useRef } from 'react'
import { useSession } from 'next-auth/react'

interface ChatMessage {
  id: string | number
  name: string
  email: string
  message: string
  messageType: string
  contentType: string
  createdAt: string
  isRead?: boolean
  attachments?: Array<{
    id: string
    filename: string
    size: number
    mimeType: string
    url: string
  }>
  sender?: {
    id: string | number
    email: string
    firstname?: string
    lastname?: string
    imageurl?: string
    role: string
  }
  receiver?: {
    id: string | number
    email: string
    firstname?: string
    lastname?: string
    imageurl?: string
    role: string
  }
  parent?: {
    id: string | number
    subject: string
    message: string
    createdAt: string
  }
}

interface UseChatManualOptions {
  contactFormId: string | number
  onNewMessage?: (message: ChatMessage) => void
  onMessageUpdate?: (message: ChatMessage) => void
}

export function useChatRealtime({
  contactFormId,
  onNewMessage,
  onMessageUpdate
}: UseChatManualOptions) {
  const { data: session } = useSession()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastFetchTime, setLastFetchTime] = useState<Date | null>(null)

  // Track if we've loaded messages initially
  const hasLoadedRef = useRef(false)

  // Determine API endpoint based on user role
  const getApiEndpoint = useCallback((endpoint: string) => {
    const isAdmin = session?.user?.role === 'ADMIN'
    const baseUrl = isAdmin ? '/api/admin' : '/api/chat'
    return `${baseUrl}/contact-forms/${contactFormId}/${endpoint}`
  }, [contactFormId, session?.user?.role])

  // Manual fetch messages - NO AUTOMATIC POLLING
  const fetchMessages = useCallback(async () => {
    if (!contactFormId) return

    try {
      setIsLoading(true)
      setError(null)

      console.log(`Manual fetch from: ${getApiEndpoint('messages')}`)
      const response = await fetch(getApiEndpoint('messages'))

      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.status}`)
      }

      const data = await response.json()
      const newMessages = data.data?.messages || []

      setMessages(prevMessages => {
        // Only check for new/updated messages if we've loaded before
        if (hasLoadedRef.current && prevMessages.length > 0) {
          // Check for new messages
          const newMessagesList = newMessages.filter(msg =>
            !prevMessages.find(prev => prev.id === msg.id)
          )

          // Check for updated messages
          const updatedMessages = newMessages.filter(msg => {
            const existing = prevMessages.find(prev => prev.id === msg.id)
            return existing && (existing.isRead !== msg.isRead || existing.message !== msg.message)
          })

          // Notify about new messages
          newMessagesList.forEach(message => {
            onNewMessage?.(message)
          })

          // Notify about updated messages
          updatedMessages.forEach(message => {
            onMessageUpdate?.(message)
          })
        }

        return newMessages
      })

      setLastFetchTime(new Date())
      hasLoadedRef.current = true

    } catch (err) {
      console.error('Failed to fetch messages:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch messages')
    } finally {
      setIsLoading(false)
    }
  }, [contactFormId, onNewMessage, onMessageUpdate, getApiEndpoint])

  // Manual refresh function
  const refresh = useCallback(() => {
    return fetchMessages()
  }, [fetchMessages])

  // Initialize messages when modal opens
  const initializeMessages = useCallback(() => {
    if (!hasLoadedRef.current) {
      fetchMessages()
    }
  }, [fetchMessages])

  // Simple activity tracking (no polling)
  const trackActivity = useCallback(() => {
    // Just a placeholder for compatibility
  }, [])

  // Modal state management (no polling)
  const setModalActive = useCallback(() => {
    console.log('Modal opened - initializing messages')
    initializeMessages()
  }, [initializeMessages])

  const setModalInactive = useCallback(() => {
    console.log('Modal closed')
    // Just a placeholder for compatibility
  }, [])

  // Mark message as read
  const markAsRead = useCallback(async (messageId: string | number) => {
    try {
      const isAdmin = session?.user?.role === 'ADMIN'
      const statusEndpoint = isAdmin
        ? `/api/admin/contact-forms/messages/${messageId}/status`
        : `/api/chat/messages/${messageId}/status`

      const response = await fetch(statusEndpoint, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isread: true })
      })

      if (response.ok) {
        // Update local state immediately for better UX
        setMessages(prev => prev.map(msg =>
          msg.id === messageId ? { ...msg, isRead: true } : msg
        ))
        // Manually refresh to get server state
        await refresh()
      }
    } catch (error) {
      console.error('Failed to mark message as read:', error)
    }
  }, [session?.user?.role, refresh])

  // Send new message
  const sendMessage = useCallback(async (
    message: string,
    attachments: Array<{ id: string; filename: string; size: number; mimeType: string; url: string; path: string }> = []
  ) => {
    try {
      const response = await fetch(getApiEndpoint('messages'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message,
          attachments,
          messagetype: 'chat',
          contenttype: 'text'
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send message')
      }

      // Manually refresh messages after sending
      await refresh()

      return true
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }, [getApiEndpoint, refresh])

  return {
    messages,
    isLoading,
    error,
    lastFetchTime,
    refresh,
    markAsRead,
    sendMessage,
    trackActivity,
    setModalActive,
    setModalInactive,
    initializeMessages
  }
}
