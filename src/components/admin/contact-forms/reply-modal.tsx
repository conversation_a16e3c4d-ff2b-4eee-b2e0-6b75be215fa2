'use client'

import React, { useState, useEffect } from 'react'
import { safeToLocaleDateString } from '@/lib/date-utils'
import RichTextEditor from '@/components/ui/rich-text-editor'
import FileAttachment, { AttachmentFile } from './file-attachment'
import { ChatInterface } from '../chat/chat-interface'
import { ChatInput } from '../chat/chat-input'
import { useSession } from 'next-auth/react'
import { useChatRealtime } from '@/hooks/use-chat-realtime'


interface ReplyModalProps {
  isOpen: boolean
  onClose: () => void
  contactForm: any
  onReplySuccess: () => void
}

function ReplyModal({
  isOpen,
  onClose,
  contactForm,
  onReplySuccess
}: ReplyModalProps) {
  const { data: session } = useSession()

  const [formData, setFormData] = useState({
    subject: '',
    message: '',
  })
  const [statusData, setStatusData] = useState({
    status: '',
    isRead: false,
  })
  const [forwardData, setForwardData] = useState({
    teamMember: '',
    forwardMessage: '',
  })
  const [teamMembers, setTeamMembers] = useState([])
  const [attachments, setAttachments] = useState<AttachmentFile[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  // Chat-related state
  const [selectedMessage, setSelectedMessage] = useState<any>(null)

  // Real-time chat hooks
  // Debug: Log the contact form being passed
  console.log('ReplyModal contactForm prop:', contactForm)
  console.log('ReplyModal contactForm.id:', contactForm?.id)
  console.log('ReplyModal contactForm.name:', contactForm?.name)

  const {
    messages: chatMessages,
    isLoading: isLoadingMessages,
    sendMessage: sendChatMessage,
    markAsRead,
    refresh: refreshMessages,
    trackActivity,
    setModalActive,
    setModalInactive
  } = useChatRealtime({
    contactFormId: contactForm?.id || '',
    onNewMessage: (message) => {
      console.log('New message received:', message)
      // Auto-mark as read if message is from another user
      if (session?.user?.id !== message.sender?.id) {
        setTimeout(() => markAsRead(message.id), 1000)
      }
    }
  })

  // Debug: Log the messages being loaded
  console.log('Chat messages loaded:', chatMessages)
  console.log('Chat messages count:', chatMessages.length)

  // Typing indicator (simplified for compact design)
  const handleTypingSubmit = () => {}

  // Initialize messages when modal opens - NO POLLING
  useEffect(() => {
    if (isOpen && contactForm?.id) {
      console.log('Modal opened, initializing messages for contact form ID:', contactForm.id)
      console.log('Contact form data:', contactForm)
      setModalActive() // This will trigger initial message load
    } else {
      console.log('Modal closed')
      setModalInactive()
    }

    // Cleanup when component unmounts
    return () => {
      setModalInactive()
    }
  }, [isOpen, contactForm?.id, setModalActive, setModalInactive])

  // Mark contact form as read
  const handleMarkAsRead = async () => {
    if (!contactForm) return

    try {
      await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isread: true }),
      })
    } catch (error) {
      console.error('Failed to mark as read:', error)
    }
  }

  useEffect(() => {
    if (contactForm) {
      setFormData({
        subject: `Re: ${contactForm.subject}`,
        message: '',
      })
      setStatusData({
        status: contactForm.status || 'New',
        isRead: true, // Mark as read when admin opens the reply form
      })

      // Temporarily disable auto-mark as read to debug chat loading issue
      // handleMarkAsRead()
    }
  }, [contactForm])

  // Load team members
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const response = await fetch('/api/admin/team-members?limit=100')
        if (response.ok) {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json()
            setTeamMembers(data.data || [])
          } else {
            console.error('Team members API returned non-JSON response')
            setTeamMembers([])
          }
        } else {
          console.error('Failed to fetch team members:', response.status, response.statusText)
          setTeamMembers([])
        }
      } catch (error) {
        console.error('Failed to fetch team members:', error)
        setTeamMembers([])
      }
    }

    if (isOpen) {
      fetchTeamMembers()
    }
  }, [isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Track user activity
    trackActivity()
  }

  const handleCallPhone = () => {
    if (contactForm.phone) {
      window.open(`tel:${contactForm.phone}`, '_self')
    }
  }

  const handleForwardMessage = async () => {
    if (!forwardData.teamMember) {
      setError('Please select a team member to forward to')
      return
    }

    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/forward`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          teamMember: forwardData.teamMember,
          message: forwardData.forwardMessage || '', // Ensure message is always a string
          adminReply: formData.message, // Include admin reply in forward
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to forward message'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      // Reset forward form
      setForwardData({ teamMember: '', forwardMessage: '' })
      setError('')
      alert('Message forwarded successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to forward message'
      setError(errorMessage)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.message.trim()) {
      setError('Please enter a message')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      // Send reply with attachments
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          attachments: attachments
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to send reply'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      // Reset form but keep modal open
      setFormData({ subject: '', message: '' })
      setAttachments([])
      onReplySuccess()

      // Show success message
      setError('')
      alert('Reply sent successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send reply'
      setError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleStatusUpdate = async () => {
    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: statusData.status,
          isRead: statusData.isRead,
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to update status'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      onReplySuccess()
      setError('')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update status'
      setError(errorMessage)
    }
  }

  // Chat functions
  const handleSendChatMessage = async (message: string, attachments: AttachmentFile[]) => {
    if (!contactForm?.id || !session?.user?.id) return

    try {
      handleTypingSubmit() // Stop typing indicator
      await sendChatMessage(message, attachments)
      onReplySuccess() // Trigger refresh of parent component
    } catch (error) {
      console.error('Failed to send chat message:', error)
      setError(error instanceof Error ? error.message : 'Failed to send message')
    }
  }

  const handleMessageSelect = (message: any) => {
    setSelectedMessage(message)
    // Auto-populate reply form with selected message context
    if (message) {
      setFormData(prev => ({
        ...prev,
        subject: `Re: ${message.subject || contactForm.subject}`,
        message: ''
      }))
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  if (!isOpen || !contactForm) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-6">
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900/80 via-purple-900/20 to-slate-900/80 backdrop-blur-md" onClick={handleClose} />

        <div className="relative bg-white rounded-2xl shadow-2xl border border-white/20 w-full max-w-7xl max-h-[95vh] overflow-hidden flex flex-col">
          {/* Compact Professional Header */}
          <div className="flex items-center justify-between px-6 py-4 bg-white border-b border-slate-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-lg font-semibold text-slate-900">
                  Contact Management
                </h2>
                <p className="text-sm text-slate-500">
                  {contactForm.name} • {contactForm.email}
                </p>
              </div>
            </div>

            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="w-8 h-8 bg-slate-100 hover:bg-slate-200 rounded-lg flex items-center justify-center transition-colors disabled:opacity-50"
            >
              <svg className="w-4 h-4 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content - Compact Professional Layout */}
          <div className="flex flex-1 min-h-0">
            {/* Left Panel - Compact Original Message */}
            <div className="w-72 bg-slate-50 border-r border-slate-200 overflow-y-auto">
              <div className="p-4">
                <h3 className="text-sm font-semibold text-slate-900 mb-3">Original Message</h3>

                <div className="space-y-3">
                  {/* Contact Info */}
                  <div className="bg-white rounded-lg p-3 border border-slate-200">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-600 font-medium">From:</span>
                        <span className="text-slate-900">{contactForm.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600 font-medium">Email:</span>
                        <span className="text-slate-700">{contactForm.email}</span>
                      </div>
                      {contactForm.phone && (
                        <div className="flex justify-between items-center">
                          <span className="text-slate-600 font-medium">Phone:</span>
                          <div className="flex items-center space-x-1">
                            <span className="text-slate-700">{contactForm.phone}</span>
                            <button
                              type="button"
                              onClick={handleCallPhone}
                              className="w-5 h-5 bg-green-100 hover:bg-green-200 rounded flex items-center justify-center transition-colors"
                              title="Call"
                            >
                              <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Subject */}
                  <div className="bg-white rounded-lg p-3 border border-slate-200">
                    <span className="text-xs font-medium text-slate-600 block mb-1">Subject</span>
                    <p className="text-sm text-slate-900 font-medium">{contactForm.subject}</p>
                  </div>

                  {/* Message */}
                  <div className="bg-white rounded-lg p-3 border border-slate-200">
                    <span className="text-xs font-medium text-slate-600 block mb-1">Message</span>
                    <div className="bg-slate-50 rounded p-2 max-h-24 overflow-y-auto">
                      <p className="text-sm text-slate-700 whitespace-pre-wrap">{contactForm.message}</p>
                    </div>
                  </div>

                  {/* Timestamps */}
                  <div className="bg-white rounded-lg p-3 border border-slate-200">
                    <div className="space-y-1 text-xs">
                      <div className="flex justify-between">
                        <span className="text-slate-600 font-medium">Received:</span>
                        <span className="text-slate-500">{safeToLocaleDateString(contactForm.createdat)}</span>
                      </div>
                      {contactForm.readat && (
                        <div className="flex justify-between">
                          <span className="text-slate-600 font-medium">Read:</span>
                          <span className="text-slate-500">{safeToLocaleDateString(contactForm.readat)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content Area - Compact Professional Layout */}
            <div className="flex-1 flex flex-col bg-white min-h-0">
              {/* Top Section - Reply Form and Status Management */}
              <div className="flex border-b border-slate-200 flex-shrink-0">
                {/* Reply Form - Compact Design */}
                <div className="flex-1 p-4">
                  <h3 className="text-sm font-semibold text-slate-900 mb-3">Send Reply</h3>

                  {error && (
                    <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-3">
                    {/* Subject Input */}
                    <input
                      type="text"
                      placeholder="Subject"
                      value={formData.subject}
                      onChange={(e) => {
                        handleInputChange(e)
                        trackActivity()
                      }}
                      className="w-full px-3 py-2 text-sm bg-white border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-colors"
                      disabled={isSubmitting}
                      name="subject"
                    />

                    {/* Message Editor */}
                    <div className="bg-white rounded-lg border border-slate-300 overflow-hidden">
                      <RichTextEditor
                        content={formData.message}
                        onChange={(content) => {
                          setFormData(prev => ({ ...prev, message: content }))
                          trackActivity()
                        }}
                        placeholder="Type your reply message here..."
                        disabled={isSubmitting}
                        className="h-24"
                      />

                      {/* Bottom Bar */}
                      <div className="flex items-center justify-between p-2 bg-slate-50 border-t border-slate-200">
                        <div className="flex-1">
                          <FileAttachment
                            attachments={attachments}
                            onAttachmentsChange={setAttachments}
                            disabled={isSubmitting}
                            maxFiles={5}
                            className=""
                          />
                        </div>

                        <button
                          type="submit"
                          disabled={isSubmitting || !formData.message.trim()}
                          className="ml-2 w-8 h-8 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
                          title="Send Reply"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>

                {/* Status Management - Compact Design */}
                <div className="w-64 border-l border-slate-200 p-4 bg-slate-50">
                  <h3 className="text-sm font-semibold text-slate-900 mb-3">Status & Actions</h3>

                  <div className="space-y-3">
                    {/* Status */}
                    <div>
                      <label className="text-xs font-medium text-slate-600 block mb-1">Status</label>
                      <select
                        value={statusData.status}
                        onChange={(e) => setStatusData(prev => ({ ...prev, status: e.target.value }))}
                        className="w-full px-2 py-1.5 text-sm bg-white border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        disabled={isSubmitting}
                      >
                        <option value="">Select Status</option>
                        <option value="pending">🟡 Pending</option>
                        <option value="in_progress">🔵 In Progress</option>
                        <option value="resolved">🟢 Resolved</option>
                        <option value="closed">⚫ Closed</option>
                      </select>
                    </div>

                    {/* Read Status */}
                    <div>
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={statusData.isRead}
                          onChange={(e) => setStatusData(prev => ({ ...prev, isRead: e.target.checked }))}
                          className="w-3 h-3 text-blue-600 bg-white border-slate-300 rounded focus:ring-blue-500 focus:ring-1"
                          disabled={isSubmitting}
                        />
                        <span className="text-sm text-slate-700">Mark as Read</span>
                      </label>
                    </div>

                    {/* Update Button */}
                    <button
                      type="button"
                      onClick={handleStatusUpdate}
                      disabled={isSubmitting}
                      className="w-full px-3 py-1.5 text-sm font-medium bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
                    >
                      Update Status
                    </button>
                  </div>

                  {/* Forward Section */}
                  <div className="pt-3 border-t border-slate-200">
                    <label className="text-xs font-medium text-slate-600 block mb-1">Forward to Team</label>
                    <select
                      value={forwardData.teamMember}
                      onChange={(e) => setForwardData(prev => ({ ...prev, teamMember: e.target.value }))}
                      className="w-full px-2 py-1.5 text-sm bg-white border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 mb-2"
                      disabled={isSubmitting}
                    >
                      <option value="">Select Team Member</option>
                      {teamMembers.map((member: any) => (
                        <option key={member.id} value={member.id}>
                          {member.firstname} {member.lastname}
                        </option>
                      ))}
                    </select>

                    <textarea
                      placeholder="Forward message (optional)"
                      value={forwardData.forwardMessage}
                      onChange={(e) => setForwardData(prev => ({ ...prev, forwardMessage: e.target.value }))}
                      className="w-full px-2 py-1.5 text-sm bg-white border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none mb-2"
                      rows={2}
                      disabled={isSubmitting}
                    />

                    <button
                      type="button"
                      onClick={handleForwardMessage}
                      disabled={isSubmitting || !forwardData.teamMember}
                      className="w-full px-3 py-1.5 text-sm font-medium bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 transition-colors"
                    >
                      Forward
                    </button>
                  </div>
                </div>
              </div>

              {/* Bottom Section - Compact Chat Interface */}
              <div className="flex-1 border-t border-slate-200 min-h-0">
                <div className="h-full flex flex-col bg-white">
                  {/* Chat Header - Compact Design */}
                  <div className="flex items-center justify-between px-4 py-2 border-b border-slate-200 bg-slate-50">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-sm font-semibold text-slate-900">Chat Messages</h3>
                        <p className="text-xs text-slate-500">{chatMessages.length} messages</p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        refreshMessages()
                        trackActivity()
                      }}
                      disabled={isLoadingMessages}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
                      title="Refresh messages"
                    >
                      {isLoadingMessages ? 'Loading...' : 'Refresh'}
                    </button>
                  </div>

                  {/* Modern Chat Interface */}
                  <div className="flex-1 flex flex-col min-h-0">
                    <div className="flex-1 bg-gradient-to-b from-slate-50/30 to-white overflow-y-auto">
                      <ChatInterface
                        messages={chatMessages}
                        currentUserId={session?.user?.id || ''}
                        isLoading={isLoadingMessages}
                        onMessageSelect={handleMessageSelect}
                        selectedMessageId={selectedMessage?.id}
                        showTypingIndicator={false}
                        typingUsers={[]}
                        autoScroll={true}
                        className="h-full"
                      />
                    </div>

                    {/* Modern Chat Input */}
                    <div className="border-t border-slate-200/60 bg-gradient-to-r from-white to-slate-50/50">
                      <ChatInput
                        onSendMessage={handleSendChatMessage}
                        placeholder="Type your chat message..."
                        disabled={isSubmitting}
                        showRichText={false}
                        maxFiles={3}
                        onTypingStart={() => {}}
                        onTypingStop={() => {}}
                        className="border-t-0"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReplyModal
