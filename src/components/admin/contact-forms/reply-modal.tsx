'use client'

import React, { useState, useEffect } from 'react'
import { safeToLocaleDateString } from '@/lib/date-utils'
import RichTextEditor from '@/components/ui/rich-text-editor'
import FileAttachment, { AttachmentFile } from './file-attachment'
import { ChatInterface } from '../chat/chat-interface'
import { ChatInput } from '../chat/chat-input'
import { useSession } from 'next-auth/react'
import { useChatRealtime } from '@/hooks/use-chat-realtime'


interface ReplyModalProps {
  isOpen: boolean
  onClose: () => void
  contactForm: any
  onReplySuccess: () => void
}

function ReplyModal({
  isOpen,
  onClose,
  contactForm,
  onReplySuccess
}: ReplyModalProps) {
  const { data: session } = useSession()

  const [formData, setFormData] = useState({
    subject: '',
    message: '',
  })
  const [statusData, setStatusData] = useState({
    status: '',
    isRead: false,
  })
  const [forwardData, setForwardData] = useState({
    teamMember: '',
    forwardMessage: '',
  })
  const [teamMembers, setTeamMembers] = useState([])
  const [attachments, setAttachments] = useState<AttachmentFile[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  // Chat-related state
  const [selectedMessage, setSelectedMessage] = useState<any>(null)

  // Real-time chat hooks
  const {
    messages: chatMessages,
    isLoading: isLoadingMessages,
    sendMessage: sendChatMessage,
    markAsRead,
    refresh: refreshMessages,
    trackActivity,
    setModalActive,
    setModalInactive
  } = useChatRealtime({
    contactFormId: contactForm?.id || '',
    onNewMessage: (message) => {
      console.log('New message received:', message)
      // Auto-mark as read if message is from another user
      if (session?.user?.id !== message.sender?.id) {
        setTimeout(() => markAsRead(message.id), 1000)
      }
    }
  })

  // Typing indicator (simplified for compact design)
  const handleTypingSubmit = () => {}

  // Initialize messages when modal opens - NO POLLING
  useEffect(() => {
    if (isOpen && contactForm?.id) {
      console.log('Modal opened, initializing messages')
      setModalActive() // This will trigger initial message load
    } else {
      console.log('Modal closed')
      setModalInactive()
    }

    // Cleanup when component unmounts
    return () => {
      setModalInactive()
    }
  }, [isOpen, contactForm?.id, setModalActive, setModalInactive])

  // Mark contact form as read
  const handleMarkAsRead = async () => {
    if (!contactForm) return

    try {
      await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isread: true }),
      })
    } catch (error) {
      console.error('Failed to mark as read:', error)
    }
  }

  useEffect(() => {
    if (contactForm) {
      setFormData({
        subject: `Re: ${contactForm.subject}`,
        message: '',
      })
      setStatusData({
        status: contactForm.status || 'New',
        isRead: true, // Mark as read when admin opens the reply form
      })

      // Mark the contact form as read when opening the reply modal
      handleMarkAsRead()
    }
  }, [contactForm])

  // Load team members
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const response = await fetch('/api/admin/team-members?limit=100')
        if (response.ok) {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json()
            setTeamMembers(data.data || [])
          } else {
            console.error('Team members API returned non-JSON response')
            setTeamMembers([])
          }
        } else {
          console.error('Failed to fetch team members:', response.status, response.statusText)
          setTeamMembers([])
        }
      } catch (error) {
        console.error('Failed to fetch team members:', error)
        setTeamMembers([])
      }
    }

    if (isOpen) {
      fetchTeamMembers()
    }
  }, [isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Track user activity
    trackActivity()
  }

  const handleCallPhone = () => {
    if (contactForm.phone) {
      window.open(`tel:${contactForm.phone}`, '_self')
    }
  }

  const handleForwardMessage = async () => {
    if (!forwardData.teamMember) {
      setError('Please select a team member to forward to')
      return
    }

    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/forward`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          teamMember: forwardData.teamMember,
          message: forwardData.forwardMessage || '', // Ensure message is always a string
          adminReply: formData.message, // Include admin reply in forward
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to forward message'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      // Reset forward form
      setForwardData({ teamMember: '', forwardMessage: '' })
      setError('')
      alert('Message forwarded successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to forward message'
      setError(errorMessage)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.message.trim()) {
      setError('Please enter a message')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      // Send reply with attachments
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          attachments: attachments
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to send reply'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      // Reset form but keep modal open
      setFormData({ subject: '', message: '' })
      setAttachments([])
      onReplySuccess()

      // Show success message
      setError('')
      alert('Reply sent successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send reply'
      setError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleStatusUpdate = async () => {
    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: statusData.status,
          isRead: statusData.isRead,
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to update status'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      onReplySuccess()
      setError('')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update status'
      setError(errorMessage)
    }
  }

  // Chat functions
  const handleSendChatMessage = async (message: string, attachments: AttachmentFile[]) => {
    if (!contactForm?.id || !session?.user?.id) return

    try {
      handleTypingSubmit() // Stop typing indicator
      await sendChatMessage(message, attachments)
      onReplySuccess() // Trigger refresh of parent component
    } catch (error) {
      console.error('Failed to send chat message:', error)
      setError(error instanceof Error ? error.message : 'Failed to send message')
    }
  }

  const handleMessageSelect = (message: any) => {
    setSelectedMessage(message)
    // Auto-populate reply form with selected message context
    if (message) {
      setFormData(prev => ({
        ...prev,
        subject: `Re: ${message.subject || contactForm.subject}`,
        message: ''
      }))
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  if (!isOpen || !contactForm) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-6">
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900/80 via-purple-900/20 to-slate-900/80 backdrop-blur-md" onClick={handleClose} />

        <div className="relative bg-white rounded-2xl shadow-2xl border border-white/20 w-full max-w-7xl max-h-[95vh] overflow-hidden flex flex-col">
          {/* Modern Header with Gradient */}
          <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 p-6">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-white">
                      Contact Management
                    </h2>
                    <p className="text-blue-100 text-sm font-medium">
                      Reply • Chat • Status • Forward
                    </p>
                  </div>
                </div>
              </div>

              <button
                onClick={handleClose}
                disabled={isSubmitting}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-all duration-200 backdrop-blur-sm disabled:opacity-50"
              >
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Content - Modern Single Layout */}
          <div className="flex flex-1 min-h-0">
            {/* Left Panel - Original Message (Modern Card Design) */}
            <div className="w-80 bg-gradient-to-b from-slate-50 to-white border-r border-slate-200/60 overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-sm font-bold text-slate-800">Original Message</h3>
                </div>

                <div className="space-y-4">
                  {/* Contact Info Card */}
                  <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200/60">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">From</span>
                        <span className="text-sm font-medium text-slate-900">{contactForm.name}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">Email</span>
                        <span className="text-sm text-slate-700">{contactForm.email}</span>
                      </div>

                      {contactForm.phone && (
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">Phone</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-slate-700">{contactForm.phone}</span>
                            <button
                              type="button"
                              onClick={handleCallPhone}
                              className="w-6 h-6 bg-green-100 hover:bg-green-200 rounded-full flex items-center justify-center transition-colors"
                              title="Call this number"
                            >
                              <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Subject Card */}
                  <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200/60">
                    <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide block mb-2">Subject</span>
                    <p className="text-sm font-medium text-slate-900">{contactForm.subject}</p>
                  </div>

                  {/* Message Card */}
                  <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200/60">
                    <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide block mb-2">Message</span>
                    <div className="bg-slate-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                      <p className="text-sm text-slate-700 whitespace-pre-wrap leading-relaxed">{contactForm.message}</p>
                    </div>
                  </div>

                  {/* Timestamps Card */}
                  <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200/60">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">Received</span>
                        <span className="text-xs text-slate-500">{safeToLocaleDateString(contactForm.createdat)}</span>
                      </div>

                      {contactForm.readat && (
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-semibold text-slate-600 uppercase tracking-wide">Read</span>
                          <span className="text-xs text-slate-500">{safeToLocaleDateString(contactForm.readat)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content Area - Modern Layout */}
            <div className="flex-1 flex flex-col bg-gradient-to-b from-white to-slate-50/30 min-h-0">
              {/* Top Section - Reply Form and Status Management */}
              <div className="flex border-b border-slate-200/60 flex-shrink-0">
                {/* Reply Form - Modern Design */}
                <div className="flex-1 p-6">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                      </svg>
                    </div>
                    <h3 className="text-sm font-bold text-slate-800">Send Reply</h3>
                  </div>

                  {error && (
                    <div className="mb-4 p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl">
                      <div className="flex items-center space-x-2">
                        <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p className="text-sm text-red-700 font-medium">{error}</p>
                      </div>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Subject Input - Modern Design */}
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Subject"
                        value={formData.subject}
                        onChange={(e) => {
                          handleInputChange(e)
                          trackActivity()
                        }}
                        className="w-full px-4 py-3 text-sm bg-white border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 placeholder-slate-400"
                        disabled={isSubmitting}
                        name="subject"
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 pointer-events-none"></div>
                    </div>

                    {/* Message Editor - Modern Design */}
                    <div className="relative">
                      <div className="bg-white rounded-xl border border-slate-300 overflow-hidden shadow-sm">
                        <RichTextEditor
                          content={formData.message}
                          onChange={(content) => {
                            setFormData(prev => ({ ...prev, message: content }))
                            trackActivity()
                          }}
                          placeholder="Type your reply message here..."
                          disabled={isSubmitting}
                          className="h-32"
                        />

                        {/* Bottom Bar with File Attachment and Send Button */}
                        <div className="flex items-center justify-between p-3 bg-slate-50 border-t border-slate-200">
                          {/* File Attachment */}
                          <div className="flex-1">
                            <FileAttachment
                              attachments={attachments}
                              onAttachmentsChange={setAttachments}
                              disabled={isSubmitting}
                              maxFiles={5}
                              className=""
                            />
                          </div>

                          {/* Send Button - Modern Design */}
                          <button
                            type="submit"
                            disabled={isSubmitting || !formData.message.trim()}
                            className="ml-3 w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                            title="Send Reply"
                          >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>

                {/* Status Management - Modern Design */}
                <div className="w-72 border-l border-slate-200/60 p-6 bg-gradient-to-b from-slate-50/50 to-white">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-sm font-bold text-slate-800">Status Management</h3>
                  </div>

                  <div className="space-y-4">
                    {/* Status Selection Card */}
                    <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200/60">
                      <label className="block text-xs font-semibold text-slate-600 uppercase tracking-wide mb-2">Status</label>
                      <select
                        value={statusData.status}
                        onChange={(e) => setStatusData(prev => ({ ...prev, status: e.target.value }))}
                        className="w-full px-3 py-2 text-sm bg-slate-50 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200"
                      >
                        <option value="pending">🟡 Pending</option>
                        <option value="in-progress">🔵 In Progress</option>
                        <option value="resolved">🟢 Resolved</option>
                        <option value="closed">⚫ Closed</option>
                      </select>
                    </div>

                    {/* Read Status Card */}
                    <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200/60">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          id="isRead"
                          checked={statusData.isRead}
                          onChange={(e) => setStatusData(prev => ({ ...prev, isRead: e.target.checked }))}
                          className="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <label htmlFor="isRead" className="text-sm font-medium text-slate-700">
                          Mark as read
                        </label>
                      </div>
                    </div>

                    {/* Update Button */}
                    <button
                      type="button"
                      onClick={handleStatusUpdate}
                      className="w-full px-4 py-3 text-sm font-medium bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      Update Status
                    </button>
                  </div>

                  {/* Forward Section */}
                  <div className="mt-6 pt-6 border-t border-slate-200/60">
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-md flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                        </svg>
                      </div>
                      <h4 className="text-sm font-bold text-slate-800">Forward Message</h4>
                    </div>

                    <div className="space-y-3">
                      {/* Team Member Selection Card */}
                      <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200/60">
                        <label className="block text-xs font-semibold text-slate-600 uppercase tracking-wide mb-2">Team Member</label>
                        <select
                          value={forwardData.teamMember}
                          onChange={(e) => setForwardData(prev => ({ ...prev, teamMember: e.target.value }))}
                          className="w-full px-3 py-2 text-sm bg-slate-50 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-200"
                        >
                          <option value="">Select team member</option>
                          {teamMembers.map((member: any) => (
                            <option key={member.id} value={member.id}>
                              {member.firstname} {member.lastname}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Forward Message Card */}
                      <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200/60">
                        <label className="block text-xs font-semibold text-slate-600 uppercase tracking-wide mb-2">Message</label>
                        <textarea
                          value={forwardData.forwardMessage}
                          onChange={(e) => setForwardData(prev => ({ ...prev, forwardMessage: e.target.value }))}
                          placeholder="Add a note for the team member..."
                          className="w-full px-3 py-2 text-sm bg-slate-50 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-all duration-200 h-16 resize-none"
                        />
                      </div>

                      {/* Forward Button */}
                      <button
                        type="button"
                        onClick={handleForwardMessage}
                        disabled={!forwardData.teamMember}
                        className="w-full px-4 py-3 text-sm font-medium bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        Forward Message
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bottom Section - Modern Chat Interface */}
              <div className="flex-1 border-t border-slate-200/60 min-h-0">
                <div className="h-full flex flex-col bg-gradient-to-b from-white to-slate-50/30">
                  {/* Chat Header - Modern Design */}
                  <div className="flex items-center justify-between p-4 border-b border-slate-200/60 bg-gradient-to-r from-slate-50 to-white">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-sm font-bold text-slate-800">
                          Chat Messages
                        </h3>
                        <p className="text-xs text-slate-500">
                          {chatMessages.length} message{chatMessages.length !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        refreshMessages()
                        trackActivity()
                      }}
                      disabled={isLoadingMessages}
                      className="px-4 py-2 text-sm font-medium bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                      title="Refresh messages"
                    >
                      <div className="flex items-center space-x-2">
                        <svg
                          className={`w-4 h-4 ${isLoadingMessages ? 'animate-spin' : ''}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span>{isLoadingMessages ? 'Loading' : 'Refresh'}</span>
                      </div>
                    </button>
                  </div>

                  {/* Modern Chat Interface */}
                  <div className="flex-1 flex flex-col min-h-0">
                    <div className="flex-1 bg-gradient-to-b from-slate-50/30 to-white overflow-y-auto">
                      <ChatInterface
                        messages={chatMessages}
                        currentUserId={session?.user?.id || ''}
                        isLoading={isLoadingMessages}
                        onMessageSelect={handleMessageSelect}
                        selectedMessageId={selectedMessage?.id}
                        showTypingIndicator={false}
                        typingUsers={[]}
                        autoScroll={true}
                        className="h-full"
                      />
                    </div>

                    {/* Modern Chat Input */}
                    <div className="border-t border-slate-200/60 bg-gradient-to-r from-white to-slate-50/50">
                      <ChatInput
                        onSendMessage={handleSendChatMessage}
                        placeholder="Type your chat message..."
                        disabled={isSubmitting}
                        showRichText={false}
                        maxFiles={3}
                        onTypingStart={() => {}}
                        onTypingStop={() => {}}
                        className="border-t-0"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReplyModal
