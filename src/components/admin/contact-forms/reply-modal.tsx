'use client'

import React, { useState, useEffect } from 'react'
import { safeToLocaleDateString } from '@/lib/date-utils'
import RichTextEditor from '@/components/ui/rich-text-editor'
import FileAttachment, { AttachmentFile } from './file-attachment'
import { ChatInterface } from '../chat/chat-interface'
import { ChatInput } from '../chat/chat-input'
import { useSession } from 'next-auth/react'
import { useChatRealtime } from '@/hooks/use-chat-realtime'


interface ReplyModalProps {
  isOpen: boolean
  onClose: () => void
  contactForm: any
  onReplySuccess: () => void
}

function ReplyModal({
  isOpen,
  onClose,
  contactForm,
  onReplySuccess
}: ReplyModalProps) {
  const { data: session } = useSession()

  const [formData, setFormData] = useState({
    subject: '',
    message: '',
  })
  const [statusData, setStatusData] = useState({
    status: '',
    isRead: false,
  })
  const [forwardData, setForwardData] = useState({
    teamMember: '',
    forwardMessage: '',
  })
  const [teamMembers, setTeamMembers] = useState([])
  const [attachments, setAttachments] = useState<AttachmentFile[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  // Chat-related state
  const [selectedMessage, setSelectedMessage] = useState<any>(null)

  // Real-time chat hooks
  const {
    messages: chatMessages,
    isLoading: isLoadingMessages,
    sendMessage: sendChatMessage,
    markAsRead,
    refresh: refreshMessages,
    trackActivity,
    setModalActive,
    setModalInactive,
    initializeMessages
  } = useChatRealtime({
    contactFormId: contactForm?.id || '',
    onNewMessage: (message) => {
      console.log('New message received:', message)
      // Auto-mark as read if message is from another user
      if (session?.user?.id !== message.sender?.id) {
        setTimeout(() => markAsRead(message.id), 1000)
      }
    }
  })

  // Typing indicator (simplified for compact design)
  const handleTypingSubmit = () => {}

  // Initialize messages when modal opens - NO POLLING
  useEffect(() => {
    if (isOpen && contactForm?.id) {
      console.log('Modal opened, initializing messages')
      setModalActive() // This will trigger initial message load
    } else {
      console.log('Modal closed')
      setModalInactive()
    }

    // Cleanup when component unmounts
    return () => {
      setModalInactive()
    }
  }, [isOpen, contactForm?.id, setModalActive, setModalInactive])

  // Mark contact form as read
  const handleMarkAsRead = async () => {
    if (!contactForm) return

    try {
      await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isread: true }),
      })
    } catch (error) {
      console.error('Failed to mark as read:', error)
    }
  }

  useEffect(() => {
    if (contactForm) {
      setFormData({
        subject: `Re: ${contactForm.subject}`,
        message: '',
      })
      setStatusData({
        status: contactForm.status || 'New',
        isRead: true, // Mark as read when admin opens the reply form
      })

      // Mark the contact form as read when opening the reply modal
      handleMarkAsRead()
    }
  }, [contactForm])

  // Load team members
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const response = await fetch('/api/admin/team-members?limit=100')
        if (response.ok) {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json()
            setTeamMembers(data.data || [])
          } else {
            console.error('Team members API returned non-JSON response')
            setTeamMembers([])
          }
        } else {
          console.error('Failed to fetch team members:', response.status, response.statusText)
          setTeamMembers([])
        }
      } catch (error) {
        console.error('Failed to fetch team members:', error)
        setTeamMembers([])
      }
    }

    if (isOpen) {
      fetchTeamMembers()
    }
  }, [isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Track user activity
    trackActivity()
  }

  const handleCallPhone = () => {
    if (contactForm.phone) {
      window.open(`tel:${contactForm.phone}`, '_self')
    }
  }

  const handleForwardMessage = async () => {
    if (!forwardData.teamMember) {
      setError('Please select a team member to forward to')
      return
    }

    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/forward`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          teamMember: forwardData.teamMember,
          message: forwardData.forwardMessage || '', // Ensure message is always a string
          adminReply: formData.message, // Include admin reply in forward
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to forward message'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      // Reset forward form
      setForwardData({ teamMember: '', forwardMessage: '' })
      setError('')
      alert('Message forwarded successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to forward message'
      setError(errorMessage)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.message.trim()) {
      setError('Please enter a message')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      // Send reply with attachments
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          attachments: attachments
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to send reply'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      // Reset form but keep modal open
      setFormData({ subject: '', message: '' })
      setAttachments([])
      onReplySuccess()

      // Show success message
      setError('')
      alert('Reply sent successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send reply'
      setError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleStatusUpdate = async () => {
    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: statusData.status,
          isRead: statusData.isRead,
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to update status'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      onReplySuccess()
      setError('')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update status'
      setError(errorMessage)
    }
  }

  // Chat functions
  const handleSendChatMessage = async (message: string, attachments: AttachmentFile[]) => {
    if (!contactForm?.id || !session?.user?.id) return

    try {
      handleTypingSubmit() // Stop typing indicator
      await sendChatMessage(message, attachments)
      onReplySuccess() // Trigger refresh of parent component
    } catch (error) {
      console.error('Failed to send chat message:', error)
      setError(error instanceof Error ? error.message : 'Failed to send message')
    }
  }

  const handleMessageSelect = (message: any) => {
    setSelectedMessage(message)
    // Auto-populate reply form with selected message context
    if (message) {
      setFormData(prev => ({
        ...prev,
        subject: `Re: ${message.subject || contactForm.subject}`,
        message: ''
      }))
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  if (!isOpen || !contactForm) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-6">
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" onClick={handleClose} />

        <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-7xl max-h-[95vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Contact Form Management
              </h2>
              <span className="text-sm text-gray-500">
                Reply • Chat • Status Management
              </span>
            </div>

            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content - Compact Single Layout */}
          <div className="flex h-[calc(95vh-80px)]">
            {/* Left Panel - Original Message (Compact) */}
            <div className="w-80 border-r border-gray-200 bg-gray-50 overflow-y-auto">
              <div className="p-4">
                <h3 className="text-sm font-semibold text-gray-900 mb-3">Original Message</h3>

                <div className="space-y-3 text-xs">
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">From:</span>
                    <span className="text-gray-900">{contactForm.name}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="font-medium text-gray-700">Email:</span>
                    <span className="text-gray-900">{contactForm.email}</span>
                  </div>

                  {contactForm.phone && (
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-gray-700">Phone:</span>
                      <div className="flex items-center space-x-1">
                        <span className="text-gray-900">{contactForm.phone}</span>
                        <button
                          type="button"
                          onClick={handleCallPhone}
                          className="text-blue-600 hover:text-blue-800 text-xs"
                          title="Call this number"
                        >
                          📞
                        </button>
                      </div>
                    </div>
                  )}

                  <div>
                    <span className="font-medium text-gray-700 block mb-1">Subject:</span>
                    <p className="text-gray-900 text-xs">{contactForm.subject}</p>
                  </div>

                  <div>
                    <span className="font-medium text-gray-700 block mb-1">Message:</span>
                    <div className="p-2 bg-white border border-gray-200 rounded text-xs max-h-32 overflow-y-auto">
                      <p className="text-gray-900 whitespace-pre-wrap">{contactForm.message}</p>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Received:</span>
                    <span className="text-gray-500">{safeToLocaleDateString(contactForm.createdat)}</span>
                  </div>

                  {contactForm.readat && (
                    <div className="flex justify-between">
                      <span className="font-medium text-gray-700">Read:</span>
                      <span className="text-gray-500">{safeToLocaleDateString(contactForm.readat)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Main Content Area - All Components */}
            <div className="flex-1 flex flex-col">
              {/* Top Section - Reply Form and Status Management */}
              <div className="flex border-b border-gray-200">
                {/* Reply Form */}
                <div className="flex-1 p-4">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">Send Reply</h3>

                  {error && (
                    <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-xs">
                      <p className="text-red-600">{error}</p>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-3">
                    <div>
                      <input
                        type="text"
                        placeholder="Subject"
                        value={formData.subject}
                        onChange={(e) => {
                          handleInputChange(e)
                          trackActivity()
                        }}
                        className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        disabled={isSubmitting}
                        name="subject"
                      />
                    </div>

                    <div className="relative">
                      <RichTextEditor
                        content={formData.message}
                        onChange={(content) => {
                          setFormData(prev => ({ ...prev, message: content }))
                          trackActivity()
                        }}
                        placeholder="Type your reply message here..."
                        disabled={isSubmitting}
                        className="h-32"
                      />
                      {/* Bottom Bar with File Attachment and Send Button */}
                      <div className="absolute bottom-1 left-1 right-1 flex items-end space-x-2 z-10">
                        {/* File Attachment */}
                        <div className="flex-1">
                          <FileAttachment
                            attachments={attachments}
                            onAttachmentsChange={setAttachments}
                            disabled={isSubmitting}
                            maxFiles={5}
                            className=""
                          />
                        </div>

                        {/* Send Button - Fixed width */}
                        <button
                          type="submit"
                          disabled={isSubmitting || !formData.message.trim()}
                          className="w-8 h-8 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
                          title="Send Reply"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>

                {/* Status Management */}
                <div className="w-64 border-l border-gray-200 p-4">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">Status Management</h3>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Status</label>
                      <select
                        value={statusData.status}
                        onChange={(e) => setStatusData(prev => ({ ...prev, status: e.target.value }))}
                        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="pending">Pending</option>
                        <option value="in-progress">In Progress</option>
                        <option value="resolved">Resolved</option>
                        <option value="closed">Closed</option>
                      </select>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isRead"
                        checked={statusData.isRead}
                        onChange={(e) => setStatusData(prev => ({ ...prev, isRead: e.target.checked }))}
                        className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isRead" className="ml-2 text-xs text-gray-700">
                        Mark as read
                      </label>
                    </div>

                    <button
                      type="button"
                      onClick={handleStatusUpdate}
                      className="w-full px-3 py-1.5 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                    >
                      Update Status
                    </button>
                  </div>

                  {/* Forward Section */}
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <h4 className="text-xs font-semibold text-gray-900 mb-2">Forward to Team</h4>
                    <div className="space-y-2">
                      <select
                        value={forwardData.teamMember}
                        onChange={(e) => setForwardData(prev => ({ ...prev, teamMember: e.target.value }))}
                        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="">Select team member</option>
                        {teamMembers.map((member: any) => (
                          <option key={member.id} value={member.id}>
                            {member.firstname} {member.lastname}
                          </option>
                        ))}
                      </select>

                      <textarea
                        value={forwardData.forwardMessage}
                        onChange={(e) => setForwardData(prev => ({ ...prev, forwardMessage: e.target.value }))}
                        placeholder="Forward message..."
                        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 h-16 resize-none"
                      />

                      <button
                        type="button"
                        onClick={handleForwardMessage}
                        disabled={!forwardData.teamMember}
                        className="w-full px-3 py-1.5 text-xs bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 transition-colors"
                      >
                        Forward
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bottom Section - Chat Interface */}
              <div className="flex-1 border-t border-gray-200">
                <div className="h-full flex flex-col">
                  {/* Chat Header */}
                  <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
                    <h3 className="text-sm font-semibold text-gray-900">
                      Chat Messages ({chatMessages.length})
                    </h3>
                    <button
                      type="button"
                      onClick={() => {
                        refreshMessages()
                        trackActivity()
                      }}
                      disabled={isLoadingMessages}
                      className="flex items-center space-x-1 px-2 py-1 text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50"
                      title="Refresh messages"
                    >
                      <svg
                        className={`w-3 h-3 ${isLoadingMessages ? 'animate-spin' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      <span>Refresh</span>
                    </button>
                  </div>

                  {/* Chat Interface */}
                  <div className="flex-1 flex flex-col">
                    <div className="flex-1">
                      <ChatInterface
                        messages={chatMessages}
                        currentUserId={session?.user?.id || ''}
                        isLoading={isLoadingMessages}
                        onMessageSelect={handleMessageSelect}
                        selectedMessageId={selectedMessage?.id}
                        showTypingIndicator={false}
                        typingUsers={[]}
                        autoScroll={true}
                        className="h-full"
                      />
                    </div>

                    {/* Chat Input */}
                    <div className="border-t border-gray-200">
                      <ChatInput
                        onSendMessage={handleSendChatMessage}
                        placeholder="Type your chat message..."
                        disabled={isSubmitting}
                        showRichText={false}
                        maxFiles={3}
                        onTypingStart={() => {}}
                        onTypingStop={() => {}}
                        className="border-t-0"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReplyModal
