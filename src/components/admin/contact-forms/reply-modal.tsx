'use client'

import React, { useState, useEffect } from 'react'
import { safeToLocaleDateString } from '@/lib/date-utils'
import RichTextEditor from '@/components/ui/rich-text-editor'
import FileAttachment, { AttachmentFile } from './file-attachment'
import { ChatInterface } from '../chat/chat-interface'
import { ChatInput } from '../chat/chat-input'
import { useSession } from 'next-auth/react'
import { useChatRealtime } from '@/hooks/use-chat-realtime'
import { useTypingIndicator } from '@/hooks/use-typing-indicator'

interface ReplyModalProps {
  isOpen: boolean
  onClose: () => void
  contactForm: any
  onReplySuccess: () => void
}

function ReplyModal({
  isOpen,
  onClose,
  contactForm,
  onReplySuccess
}: ReplyModalProps) {
  const { data: session } = useSession()

  const [formData, setFormData] = useState({
    subject: '',
    message: '',
  })
  const [statusData, setStatusData] = useState({
    status: '',
    isRead: false,
  })
  const [forwardData, setForwardData] = useState({
    teamMember: '',
    forwardMessage: '',
  })
  const [teamMembers, setTeamMembers] = useState([])
  const [attachments, setAttachments] = useState<AttachmentFile[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  // Chat-related state
  const [selectedMessage, setSelectedMessage] = useState(null)
  const [showChatView, setShowChatView] = useState(false)

  // Real-time chat hooks
  const {
    messages: chatMessages,
    isLoading: isLoadingMessages,
    sendMessage: sendChatMessage,
    markAsRead,
    refresh: refreshMessages,
    trackActivity,
    setModalActive,
    setModalInactive
  } = useChatRealtime({
    contactFormId: contactForm?.id || '',
    enabled: isOpen && !!contactForm?.id,
    pollingInterval: 60000, // Further reduced frequency: 60 seconds
    onNewMessage: (message) => {
      console.log('New message received:', message)
      // Auto-mark as read if chat view is active
      if (showChatView && session?.user?.id !== message.sender?.id) {
        setTimeout(() => markAsRead(message.id), 1000)
      }
    }
  })

  const {
    typingUsers,
    isCurrentUserTyping,
    handleInputChange: handleTypingInputChange,
    handleSubmit: handleTypingSubmit
  } = useTypingIndicator({
    contactFormId: contactForm?.id || '',
    currentUserId: session?.user?.id || '',
    enabled: isOpen && showChatView
  })

  // Control polling based on modal state
  useEffect(() => {
    if (isOpen && contactForm?.id) {
      console.log('Modal opened, activating chat polling')
      setModalActive()
    } else {
      console.log('Modal closed, deactivating chat polling')
      setModalInactive()
    }

    // Cleanup when component unmounts
    return () => {
      setModalInactive()
    }
  }, [isOpen, contactForm?.id, setModalActive, setModalInactive])

  // Mark contact form as read
  const handleMarkAsRead = async () => {
    if (!contactForm) return

    try {
      await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isread: true }),
      })
    } catch (error) {
      console.error('Failed to mark as read:', error)
    }
  }

  useEffect(() => {
    if (contactForm) {
      setFormData({
        subject: `Re: ${contactForm.subject}`,
        message: '',
      })
      setStatusData({
        status: contactForm.status || 'New',
        isRead: true, // Mark as read when admin opens the reply form
      })

      // Mark the contact form as read when opening the reply modal
      handleMarkAsRead()
    }
  }, [contactForm])

  // Load team members
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const response = await fetch('/api/admin/team-members?limit=100')
        if (response.ok) {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json()
            setTeamMembers(data.data || [])
          } else {
            console.error('Team members API returned non-JSON response')
            setTeamMembers([])
          }
        } else {
          console.error('Failed to fetch team members:', response.status, response.statusText)
          setTeamMembers([])
        }
      } catch (error) {
        console.error('Failed to fetch team members:', error)
        setTeamMembers([])
      }
    }

    if (isOpen) {
      fetchTeamMembers()
    }
  }, [isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Track user activity
    trackActivity()
  }

  const handleCallPhone = () => {
    if (contactForm.phone) {
      window.open(`tel:${contactForm.phone}`, '_self')
    }
  }

  const handleForwardMessage = async () => {
    if (!forwardData.teamMember) {
      setError('Please select a team member to forward to')
      return
    }

    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/forward`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          teamMember: forwardData.teamMember,
          message: forwardData.forwardMessage || '', // Ensure message is always a string
          adminReply: formData.message, // Include admin reply in forward
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to forward message'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      // Reset forward form
      setForwardData({ teamMember: '', forwardMessage: '' })
      setError('')
      alert('Message forwarded successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to forward message'
      setError(errorMessage)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.message.trim()) {
      setError('Please enter a message')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      // Send reply with attachments
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          attachments: attachments
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to send reply'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      onReplySuccess()
      onClose()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send reply'
      setError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleStatusUpdate = async () => {
    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: statusData.status,
          isRead: statusData.isRead,
        }),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to update status'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorMessage
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
        }
        throw new Error(errorMessage)
      }

      onReplySuccess()
      setError('')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update status'
      setError(errorMessage)
    }
  }

  // Chat functions
  const handleSendChatMessage = async (message: string, attachments: AttachmentFile[]) => {
    if (!contactForm?.id || !session?.user?.id) return

    try {
      handleTypingSubmit() // Stop typing indicator
      await sendChatMessage(message, attachments)
      onReplySuccess() // Trigger refresh of parent component
    } catch (error) {
      console.error('Failed to send chat message:', error)
      setError(error instanceof Error ? error.message : 'Failed to send message')
    }
  }

  const handleMessageSelect = (message: any) => {
    setSelectedMessage(message)
    // Auto-populate reply form with selected message context
    if (message && !showChatView) {
      setFormData(prev => ({
        ...prev,
        subject: `Re: ${message.subject || contactForm.subject}`,
        message: ''
      }))
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  if (!isOpen || !contactForm) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-6">
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" onClick={handleClose} />

        <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Contact Form Reply
              </h2>

              {/* View Toggle Buttons */}
              <div className="flex bg-white rounded-lg border border-gray-200 p-1">
                <button
                  type="button"
                  onClick={() => {
                    setShowChatView(false)
                    trackActivity()
                  }}
                  className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                    !showChatView
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Reply Form
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowChatView(true)
                    trackActivity()
                  }}
                  className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                    showChatView
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Chat View
                </button>
              </div>
            </div>

            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="flex h-[calc(90vh-120px)]">
            {/* Left Panel - Original Message */}
            <div className="w-1/3 border-r border-gray-200 bg-gray-50 overflow-y-auto">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Original Message</h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">From</label>
                    <p className="mt-1 text-sm text-gray-900">{contactForm.name}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <div className="mt-1 flex items-center space-x-2">
                      <p className="text-sm text-gray-900">{contactForm.email}</p>
                    </div>
                  </div>

                  {contactForm.phone && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Phone</label>
                      <div className="mt-1 flex items-center space-x-2">
                        <p className="text-sm text-gray-900">{contactForm.phone}</p>
                        <button
                          type="button"
                          onClick={handleCallPhone}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                          title="Call this number"
                        >
                          📞
                        </button>
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Subject</label>
                    <p className="mt-1 text-sm text-gray-900">{contactForm.subject}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Message</label>
                    <div className="mt-1 p-3 bg-white border border-gray-200 rounded-md">
                      <p className="text-sm text-gray-900 whitespace-pre-wrap">{contactForm.message}</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Received</label>
                    <p className="mt-1 text-sm text-gray-500">
                      {safeToLocaleDateString(contactForm.createdat)}
                    </p>
                  </div>

                  {contactForm.readat && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Read</label>
                      <p className="mt-1 text-sm text-gray-500">
                        {safeToLocaleDateString(contactForm.readat)}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Panel - Reply Interface */}
            <div className="flex-1 flex flex-col">
              <div className="flex-1 overflow-hidden">
                {!showChatView ? (
                  /* Reply Form View */
                  <div className="h-full flex flex-col p-6">
                    {error && (
                      <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-600">{error}</p>
                      </div>
                    )}

                    <form onSubmit={handleSubmit} className="flex-1 flex flex-col space-y-4">
                      <div>
                        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                          Subject
                        </label>
                        <input
                          type="text"
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={(e) => {
                            handleInputChange(e)
                            trackActivity()
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          disabled={isSubmitting}
                        />
                      </div>

                      <div className="flex flex-col flex-1">
                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                          Reply Message
                        </label>
                        <div className="relative flex-1">
                          <RichTextEditor
                            content={formData.message}
                            onChange={(content) => {
                              setFormData(prev => ({ ...prev, message: content }))
                              trackActivity()
                            }}
                            placeholder="Type your reply message here..."
                            disabled={isSubmitting}
                            className="h-full"
                          />
                          {/* Bottom Bar with Extended File Attachment and Send Button */}
                          <div className="absolute bottom-2 left-2 right-2 flex items-end space-x-2 z-10">
                            {/* Extended File Attachment - Takes remaining space */}
                            <div className="flex-1">
                              <FileAttachment
                                attachments={attachments}
                                onAttachmentsChange={setAttachments}
                                disabled={isSubmitting}
                                maxFiles={5}
                                className=""
                              />
                            </div>

                            {/* Send Button - Fixed width */}
                            <button
                              type="submit"
                              disabled={isSubmitting || !formData.message.trim()}
                              className="w-10 h-10 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
                              title="Send Reply"
                            >
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                ) : (
                  /* Chat View */
                  <div className="h-full flex flex-col">
                    <div className="flex-1">
                      <ChatInterface
                        messages={chatMessages}
                        currentUserId={session?.user?.id || ''}
                        isLoading={isLoadingMessages}
                        onMessageSelect={handleMessageSelect}
                        selectedMessageId={selectedMessage?.id}
                        showTypingIndicator={typingUsers.length > 0}
                        typingUsers={typingUsers}
                        autoScroll={true}
                        className="h-full"
                      />
                    </div>
                    <ChatInput
                      onSendMessage={handleSendChatMessage}
                      placeholder="Type your message..."
                      disabled={isSubmitting}
                      showRichText={false}
                      maxFiles={5}
                      onTypingStart={() => handleTypingInputChange('typing')}
                      onTypingStop={() => handleTypingInputChange('')}
                      className="border-t-0"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Footer - Status Management and Quick Actions */}
          <div className="border-t border-gray-200 bg-gray-50 p-4">
            <div className="flex items-center justify-between">
              {/* Status Management */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700">Status:</label>
                  <select
                    value={statusData.status}
                    onChange={(e) => setStatusData(prev => ({ ...prev, status: e.target.value }))}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isSubmitting}
                  >
                    <option value="New">New</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Resolved">Resolved</option>
                    <option value="Closed">Closed</option>
                  </select>
                </div>

                <button
                  type="button"
                  onClick={handleStatusUpdate}
                  disabled={isSubmitting}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  Update Status
                </button>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700">Forward to:</label>
                  <select
                    value={forwardData.teamMember}
                    onChange={(e) => setForwardData(prev => ({ ...prev, teamMember: e.target.value }))}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isSubmitting}
                  >
                    <option value="">Select team member</option>
                    {teamMembers.map((member: any) => (
                      <option key={member.id} value={member.id}>
                        {member.firstname} {member.lastname} ({member.email})
                      </option>
                    ))}
                  </select>
                </div>

                <button
                  type="button"
                  onClick={handleForwardMessage}
                  disabled={isSubmitting || !forwardData.teamMember}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  Forward
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReplyModal
